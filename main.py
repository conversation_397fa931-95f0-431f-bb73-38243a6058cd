#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黄金价格预测系统 - 重构版本
使用模块化架构，更好的项目结构
"""
import asyncio
import threading

from flask import Flask
from flask_cors import CORS

from app.config import FLASK_HOST, FLASK_PORT, FLASK_DEBUG

# from app.data_patch import run_patch
from app.core import scheduler_service
from app.api import register_routes
from app.data_patch import fetch_data


# TODO
# 1、做成自动获取行情，每日自动获取。
# 2、调用大模型时候，要获取最新的相关资讯数据。
# 3、要做成滚动，最近 50 天的胜率，以及胜率曲线变化。


def create_app():
    """创建Flask应用"""
    app = Flask(__name__, static_folder="static")

    # 启用 CORS 支持
    CORS(app)

    # 注册路由
    register_routes(app)

    return app


def run_fetch_data_in_background():
    """在后台线程中运行数据获取"""
    asyncio.run(fetch_data())


def main():
    """主函数"""
    # 创建Flask应用
    app = create_app()

    # 启动定时任务调度器
    scheduler_service.start_scheduler()

    # 在后台线程中启动数据获取
    fetch_thread = threading.Thread(target=run_fetch_data_in_background, daemon=True)
    fetch_thread.start()

    # 启动Flask应用
    app.run(host=FLASK_HOST, port=FLASK_PORT, debug=FLASK_DEBUG)


if __name__ == "__main__":
    main()
