<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汇利榜 - 多币种预测系统</title>
    <link rel="stylesheet" href="static/styles.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 30px;
            border: 1px solid #888;
            width: 90%;
            max-width: 1200px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
        }

        #ai-report-content {
            margin-top: 20px;
            line-height: 1.6;
            max-height: 700px;
            overflow-y: auto;
            padding: 10px;
            font-size: 16px;
        }

        .error {
            color: red;
        }

        .view-report-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 2px 2px;
            cursor: pointer;
            border-radius: 3px;
        }

        /* 控制面板样式 */
        .control-panel {
            background: #1e3a8a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            color: white;
        }

        .control-panel h2 {
            margin: 0 0 15px 0;
            font-size: 24px;
        }

        .control-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-weight: bold;
            font-size: 14px;
        }

        .control-group select,
        .control-group button {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
        }

        .control-group select {
            background: white;
            color: #333;
            min-width: 120px;
        }

        .control-group button {
            background: #4CAF50;
            color: white;
            transition: background 0.3s;
        }

        .control-group button:hover {
            background: #45a049;
        }

        /* 统计卡片样式 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        /* 表格容器样式 */
        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .table-header h3 {
            margin: 0;
            color: #333;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .control-row {
                flex-direction: column;
                align-items: stretch;
            }

            .stats-container {
                grid-template-columns: 1fr;
            }
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            position: relative;
            width: 80px;
            height: 80px;
            text-align: center;
        }

        .loading:after {
            content: " ";
            display: block;
            border-radius: 50%;
            width: 0;
            height: 0;
            margin: 8px;
            box-sizing: border-box;
            border: 32px solid #1e3a8a;
            border-color: #1e3a8a transparent #1e3a8a transparent;
            animation: loading 1.2s infinite;
        }

        @keyframes loading {
            0% {
                transform: rotate(0);
                animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
            }

            50% {
                transform: rotate(180deg);
                animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Markdown样式 */
        #ai-report-content h1,
        #ai-report-content h2,
        #ai-report-content h3 {
            margin-top: 16px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        #ai-report-content p {
            margin-bottom: 12px;
        }

        #ai-report-content ul,
        #ai-report-content ol {
            margin-left: 20px;
            margin-bottom: 12px;
        }

        #ai-report-content table {
            border-collapse: collapse;
            margin: 15px 0;
            width: 100%;
            background-color: #ffffff;
            border: 2px solid #ddd;
        }

        #ai-report-content table th,
        #ai-report-content table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        #ai-report-content table th {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
        }

        #ai-report-content table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        #ai-report-content table tr:hover {
            background-color: #ddd;
        }

        #ai-report-content blockquote {
            border-left: 4px solid #4CAF50;
            padding-left: 16px;
            margin-left: 0;
            color: #555;
            background-color: #f9f9f9;
            padding: 10px 16px;
        }
    </style>
</head>

<body>
    <header>
        <img src="static/logo.jpg" alt="汇利榜 Logo" class="logo">
        <h1>汇利榜 - 多币种预测系统</h1>
    </header>

    <main>
        <div class="date-time">
            <strong>当前时间：</strong>
            <span id="current-time">Loading...</span>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <h2>数据控制面板</h2>
            <div class="control-row">
                <div class="control-group">
                    <label for="symbol-select">选择交易对:</label>
                    <select id="symbol-select">
                        <option value="EURUSD">EURUSD</option>
                        <option value="GBPUSD">GBPUSD</option>
                        <option value="USDJPY">USDJPY</option>
                        <option value="USDCNY">USDCNY</option>
                    </select>
                </div>

                <div class="control-group">
                    <label for="period-select">统计周期:</label>
                    <select id="period-select">
                        <option value="7">最近一周</option>
                        <option value="30">最近一月</option>
                        <option value="90">最近一季度</option>
                        <option value="180">最近半年</option>
                        <option value="365">最近一年</option>
                        <option value="all">全部数据</option>
                    </select>
                </div>

                <div class="control-group">
                    <label>&nbsp;</label>
                    <button id="refresh-button">刷新数据</button>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-container" id="stats-container">
            <div class="stat-card">
                <h3>总预测次数</h3>
                <div class="stat-value" id="total-predictions">-</div>
                <div class="stat-label">预测记录总数</div>
            </div>
            <div class="stat-card">
                <h3>正确预测</h3>
                <div class="stat-value" id="correct-predictions">-</div>
                <div class="stat-label">方向预测正确</div>
            </div>
            <div class="stat-card">
                <h3>预测准确率</h3>
                <div class="stat-value" id="accuracy-rate">-</div>
                <div class="stat-label">正确预测占比</div>
            </div>
            <div class="stat-card">
                <h3>平均偏差</h3>
                <div class="stat-value" id="avg-deviation">-</div>
                <div class="stat-label">价格预测偏差</div>
            </div>
        </div>

        <!-- 预测记录表格 -->
        <div class="table-container">
            <div class="table-header">
                <h3>预测记录详情</h3>
            </div>
            <div id="response-container">
                <table id="predictionTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>09:00价格</th>
                            <th>预测方向</th>
                            <th>实际方向</th>
                            <th>预测16:00价格</th>
                            <th>实际16:00价格</th>
                            <th>价格差异</th>
                            <th>预测幅度(%)</th>
                            <th>价格差(实际-预测)</th>
                            <th>偏差(%)</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态插入行 -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- AI报告模态框 -->
    <div id="ai-report-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>AI分析报告</h2>
            <div id="ai-report-content"></div>
        </div>
    </div>

    <script src="static/script.js"></script>
</body>

</html>