// 更新当前日期和时间
function updateDateTime() {
    const now = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit' };
    document.getElementById('current-time').innerText = now.toLocaleString('zh-CN', options);
}

// 每秒更新时间
setInterval(updateDateTime, 1000);
updateDateTime(); // 立即执行一次

const fieldOrder = [
    "Date",
    "09:00 Price",
    "Predicted Direction",
    "Actual Direction",
    "Predicted 16:00 Price",
    "Actual 16:00 Price",
    "Price Difference",
    "Predicted Magnitude (%)",
    "Price Difference (Actual-Predicted)",
    "Deviation (%)"
];

// 全局变量
let currentSymbol = 'EURUSD';
let currentPeriod = '7';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    // 绑定事件监听器
    bindEventListeners();

    // 加载初始数据
    loadData();
});

// 绑定事件监听器
function bindEventListeners() {
    // 币种选择变化
    document.getElementById('symbol-select').addEventListener('change', function () {
        currentSymbol = this.value;
        loadData();
    });

    // 统计周期选择变化
    document.getElementById('period-select').addEventListener('change', function () {
        currentPeriod = this.value;
        loadData();
    });

    // 刷新按钮
    document.getElementById('refresh-button').addEventListener('click', function () {
        loadData();
    });

    // 关闭模态框
    document.querySelector('.close').addEventListener('click', function () {
        document.getElementById('ai-report-modal').style.display = 'none';
    });
}

// 加载数据
async function loadData() {
    try {
        showLoadingState();

        // 构建API URL - 使用新的 /api/history/<symbol> 接口
        const params = new URLSearchParams({
            period: currentPeriod
        });

        const response = await fetch(`/api/history/${currentSymbol}?${params}`);

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log(`加载的 ${currentSymbol} 数据:`, data);

        // 显示数据
        displayResults(data);
        updateStatistics(data);

    } catch (error) {
        console.error('加载数据失败:', error);
        showErrorState(error.message);
    }
}

// 显示加载状态
function showLoadingState() {
    const tableBody = document.getElementById('predictionTable').getElementsByTagName('tbody')[0];
    tableBody.innerHTML = `
        <tr>
            <td colspan="${fieldOrder.length + 1}" style="text-align: center; padding: 20px;">
                <div class="loading"></div>
                <p>正在加载数据...</p>
            </td>
        </tr>
    `;

    // 清空统计卡片
    document.getElementById('total-predictions').textContent = '-';
    document.getElementById('correct-predictions').textContent = '-';
    document.getElementById('accuracy-rate').textContent = '-';
    document.getElementById('avg-deviation').textContent = '-';
}

// 显示错误状态
function showErrorState(errorMessage) {
    const tableBody = document.getElementById('predictionTable').getElementsByTagName('tbody')[0];
    tableBody.innerHTML = `
        <tr>
            <td colspan="${fieldOrder.length + 1}" style="text-align: center; padding: 20px; color: red;">
                <p>加载数据失败: ${errorMessage}</p>
                <button onclick="loadData()" style="margin-top: 10px; padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    重试
                </button>
            </td>
        </tr>
    `;
}

// 显示结果
function displayResults(data) {
    const tableBody = document.getElementById('predictionTable').getElementsByTagName('tbody')[0];
    tableBody.innerHTML = ''; // 清空表格内容

    if (!data || data.length === 0) {
        const emptyRow = document.createElement('tr');
        const emptyCell = document.createElement('td');
        emptyCell.colSpan = fieldOrder.length + 1; // 跨越所有列 + 操作列
        emptyCell.textContent = '暂无数据';
        emptyCell.style.textAlign = 'center';
        emptyCell.style.padding = '20px';
        emptyCell.style.color = '#666';
        emptyRow.appendChild(emptyCell);
        tableBody.appendChild(emptyRow);
        return;
    }

    // 按日期降序排序
    const sortedData = data.sort((a, b) => new Date(b.Date) - new Date(a.Date));

    sortedData.forEach(row => {
        const tr = document.createElement('tr');

        // 根据预测正确性设置样式
        if (row['Predicted Direction'] === row['Actual Direction']) {
            tr.className = 'correct';
        } else if (row['Actual Direction'] && row['Actual Direction'] !== 'N/A') {
            tr.className = 'incorrect';
        }

        // 添加数据列
        fieldOrder.forEach(key => {
            const td = document.createElement('td');
            const value = row[key];

            // 格式化数值显示
            if (typeof value === 'number') {
                if (key.includes('Price') || key.includes('Difference')) {
                    td.textContent = value.toFixed(4);
                } else if (key.includes('%')) {
                    td.textContent = value.toFixed(4) + '%';
                } else {
                    td.textContent = value;
                }
            } else {
                td.textContent = value || 'N/A';
            }

            tr.appendChild(td);
        });

        // 添加操作列 - 查看AI报告按钮
        const actionTd = document.createElement('td');
        const reportBtn = document.createElement('button');
        reportBtn.className = 'view-report-btn';
        reportBtn.textContent = '查看AI报告';
        reportBtn.setAttribute('data-date', row.Date);
        reportBtn.setAttribute('data-symbol', currentSymbol);
        reportBtn.addEventListener('click', function () {
            showAIReport(this.getAttribute('data-date'), this.getAttribute('data-symbol'));
        });
        actionTd.appendChild(reportBtn);
        tr.appendChild(actionTd);

        tableBody.appendChild(tr);
    });
}

// 更新统计信息
function updateStatistics(data) {
    if (!data || data.length === 0) {
        document.getElementById('total-predictions').textContent = '0';
        document.getElementById('correct-predictions').textContent = '0';
        document.getElementById('accuracy-rate').textContent = '0%';
        document.getElementById('avg-deviation').textContent = '0%';
        return;
    }

    let totalPredictions = 0;
    let correctPredictions = 0;
    let totalDeviation = 0;
    let deviationCount = 0;

    data.forEach(row => {
        // 只统计有实际方向的数据
        if (row['Actual Direction'] && row['Actual Direction'] !== 'N/A') {
            totalPredictions++;

            if (row['Predicted Direction'] === row['Actual Direction']) {
                correctPredictions++;
            }

            // 计算偏差
            if (row['Deviation (%)'] && row['Deviation (%)'] !== 'N/A') {
                const deviation = parseFloat(row['Deviation (%)']);
                if (!isNaN(deviation)) {
                    totalDeviation += Math.abs(deviation);
                    deviationCount++;
                }
            }
        }
    });

    const accuracy = totalPredictions > 0 ? ((correctPredictions / totalPredictions) * 100).toFixed(4) : 0;
    const avgDeviation = deviationCount > 0 ? (totalDeviation / deviationCount).toFixed(4) : 0;

    // 更新统计卡片
    document.getElementById('total-predictions').textContent = totalPredictions;
    document.getElementById('correct-predictions').textContent = correctPredictions;
    document.getElementById('accuracy-rate').textContent = accuracy + '%';
    document.getElementById('avg-deviation').textContent = avgDeviation + '%';
}

// 显示AI报告模态框
function showAIReport(date, symbol) {
    // 显示模态框和加载动画
    const reportContent = document.getElementById('ai-report-content');
    const modal = document.getElementById('ai-report-modal');
    modal.style.display = 'block';
    reportContent.innerHTML = '<div class="loading"></div><p>正在生成AI分析报告，请稍候...</p>';

    // 构建API URL
    const params = new URLSearchParams({
        symbol: symbol || currentSymbol
    });

    // 创建EventSource对象连接到流式API
    const eventSource = new EventSource(`/stream_ai_report/${date}?${params}`);
    let fullReport = '';

    // 处理消息事件
    eventSource.onmessage = function (event) {
        const data = JSON.parse(event.data);

        if (data.type === 'content') {
            // 追加内容
            fullReport += data.content;
            // 使用marked解析Markdown
            try {
                const formattedReport = marked.parse(fullReport);
                reportContent.innerHTML = formattedReport;
            } catch (e) {
                // 如果解析失败，直接显示原文
                reportContent.innerHTML = fullReport.replace(/\n/g, '<br>');
            }

            // 自动滚动到底部
            reportContent.scrollTop = reportContent.scrollHeight;
        }
        else if (data.type === 'error') {
            // 显示错误信息
            reportContent.innerHTML = `<p class="error">${data.message}</p>`;
            eventSource.close();
        }
        else if (data.type === 'done') {
            // 完成时关闭连接
            eventSource.close();
            // 使用marked解析完整的Markdown
            try {
                const formattedReport = marked.parse(fullReport);
                reportContent.innerHTML = formattedReport;
            } catch (e) {
                // 如果解析失败，直接显示原文
                reportContent.innerHTML = fullReport.replace(/\n/g, '<br>');
            }
        }
    };

    // 处理错误
    eventSource.onerror = function (error) {
        console.error('EventSource错误:', error);
        reportContent.innerHTML = `<p class="error">获取报告失败，请稍后再试。</p>`;
        eventSource.close();
    };
}