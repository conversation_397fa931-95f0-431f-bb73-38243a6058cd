body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background: #ffffff;
    color: #333;
    min-height: 100vh;
}

header {
    background: #1e3a8a;
    color: white;
    padding: 20px 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

header .logo {
    height: 50px;
    vertical-align: middle;
    margin-right: 15px;
    border-radius: 8px;
}

header h1 {
    font-size: 28px;
    margin: 0;
    display: inline-block;
    vertical-align: middle;
    color: white;
    font-weight: 600;
}

main {
    padding: 30px;
    max-width: 1400px;
    margin: 20px auto;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.date-time {
    font-size: 16px;
    margin-bottom: 25px;
    padding: 15px;
    background: #1e3a8a;
    color: white;
    border-radius: 10px;
    text-align: center;
    font-weight: 500;
}

.date-time strong {
    font-weight: 600;
}

/* 控制面板样式已在HTML中定义 */

/* 统计卡片样式已在HTML中定义 */

/* 表格容器样式已在HTML中定义 */

table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

th,
td {
    border: none;
    padding: 12px 8px;
    text-align: center;
    font-size: 14px;
}

th {
    background: #1e3a8a;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 13px;
}

tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

.correct {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.incorrect {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.high-deviation {
    background-color: #fff3cd;
    color: #856404;
    border-left: 4px solid #ffc107;
}

/* 操作按钮样式 */
.view-report-btn {
    background: #1e3a8a;
    color: white;
    border: none;
    padding: 8px 16px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 12px;
    margin: 2px;
    cursor: pointer;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.view-report-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    main {
        margin: 10px;
        padding: 20px;
    }

    table {
        font-size: 12px;
    }

    th,
    td {
        padding: 8px 4px;
    }
}

@media (max-width: 768px) {
    header h1 {
        font-size: 20px;
    }

    main {
        padding: 15px;
    }

    .date-time {
        font-size: 14px;
        padding: 10px;
    }

    table {
        font-size: 11px;
    }

    th,
    td {
        padding: 6px 2px;
    }

    .view-report-btn {
        padding: 6px 12px;
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    header .logo {
        height: 40px;
    }

    header h1 {
        font-size: 18px;
    }

    main {
        padding: 10px;
    }

    table {
        font-size: 10px;
    }

    th,
    td {
        padding: 4px 1px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    position: relative;
    width: 60px;
    height: 60px;
    text-align: center;
}

.loading:after {
    content: " ";
    display: block;
    border-radius: 50%;
    width: 0;
    height: 0;
    margin: 6px;
    box-sizing: border-box;
    border: 24px solid #1e3a8a;
    border-color: #1e3a8a transparent #1e3a8a transparent;
    animation: loading 1.2s infinite;
}

@keyframes loading {
    0% {
        transform: rotate(0);
        animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

    50% {
        transform: rotate(180deg);
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 表格列宽优化 */
th:nth-child(1),
td:nth-child(1) {
    width: 100px;
    min-width: 80px;
}

th:nth-child(2),
td:nth-child(2),
th:nth-child(5),
td:nth-child(5),
th:nth-child(6),
td:nth-child(6) {
    width: 90px;
    min-width: 80px;
}

th:nth-child(3),
td:nth-child(3),
th:nth-child(4),
td:nth-child(4) {
    width: 80px;
    min-width: 70px;
}

th:nth-child(7),
td:nth-child(7),
th:nth-child(8),
td:nth-child(8),
th:nth-child(9),
td:nth-child(9),
th:nth-child(10),
td:nth-child(10) {
    width: 100px;
    min-width: 90px;
}

th:last-child,
td:last-child {
    width: 120px;
    min-width: 100px;
}

/* 数值格式化样式 */
td[data-type="price"] {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

td[data-type="percentage"] {
    font-weight: 600;
}

td[data-type="date"] {
    font-weight: 500;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-correct {
    background-color: #28a745;
}

.status-incorrect {
    background-color: #dc3545;
}

.status-pending {
    background-color: #ffc107;
}