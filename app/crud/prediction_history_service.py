"""
预测历史数据库服务模块 - 简化版本
只保留最基础的CRUD操作
"""

from datetime import datetime
from typing import List, Dict, Optional
from sqlalchemy.dialects.sqlite import insert
from app.models.prediction_history import PredictionHistory, init_prediction_history_db
from app.models.bar_data import Session
from app.logger_config import get_module_logger


class PredictionHistoryService:

    def __init__(self):
        self.session = Session()
        self.logger = get_module_logger("prediction_history_service")
        # 确保数据库表已创建
        init_prediction_history_db()

    def create(self, data: Dict) -> bool:
        """
        创建新的预测记录
        
        参数:
            data: 预测数据字典
        返回:
            bool: 是否创建成功
        """
        try:
            
            # 检查记录是否已存在
            existing = self.session.query(PredictionHistory).filter_by(
                date=data.get("Date"),
                symbol=data.get("Symbol")
            ).first()
            
            if existing:
                self.logger.warning(f"记录已存在: {data.get('Date')} {data.get('Symbol')}")
                return False
            
            # 创建新记录
            new_record = PredictionHistory.from_dict(data)
            self.session.add(new_record)
            self.session.commit()
            
            self.logger.info(f"成功创建预测记录: {data.get('Date')} {data.get('Symbol')}")
            return True
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"创建预测记录失败: {e}")
            return False

    def get_record(self, date: str, symbol) -> Optional[Dict]:
        """
        读取预测记录
        
        参数:
            date: 日期字符串 (YYYY-MM-DD)
            symbol: 交易对符号
        返回:
            Optional[Dict]: 预测数据或None
        """
        try:
            record = self.session.query(PredictionHistory).filter_by(
                date=date,
                symbol=symbol
            ).first()
            
            if record:
                self.logger.info(f"成功读取预测记录: {date} {symbol}")
                return record.to_dict()
            else:
                self.logger.warning(f"预测记录不存在: {date} {symbol}")
                return None
                
        except Exception as e:
            self.logger.error(f"读取预测记录失败: {e}")
            return None

    def update(self, data: Dict) -> bool:
        """
        更新预测记录
        
        参数:
            data: 预测数据字典
        返回:
            bool: 是否更新成功
        """
        try:
            # 确保有Symbol字段
            if "Symbol" not in data:
                data["Symbol"] = "EURUSD"
            
            # 查找现有记录
            record = self.session.query(PredictionHistory).filter_by(
                date=data.get("Date"),
                symbol=data.get("Symbol", "EURUSD")
            ).first()
            
            if not record:
                self.logger.warning(f"记录不存在，无法更新: {data.get('Date')} {data.get('Symbol')}")
                return False
            
            # 更新记录
            self._update_record_fields(record, data)
            record.updated_at = datetime.utcnow()
            self.session.commit()
            
            self.logger.info(f"成功更新预测记录: {data.get('Date')} {data.get('Symbol')}")
            return True
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"更新预测记录失败: {e}")
            return False

    def delete(self, date: str, symbol: str = "EURUSD") -> bool:
        """
        删除预测记录
        
        参数:
            date: 日期字符串 (YYYY-MM-DD)
            symbol: 交易对符号
        返回:
            bool: 是否删除成功
        """
        try:
            record = self.session.query(PredictionHistory).filter_by(
                date=date,
                symbol=symbol
            ).first()
            
            if not record:
                self.logger.warning(f"记录不存在，无法删除: {date} {symbol}")
                return False
            
            self.session.delete(record)
            self.session.commit()
            
            self.logger.info(f"成功删除预测记录: {date} {symbol}")
            return True
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"删除预测记录失败: {e}")
            return False

    def save_or_update(self, data: Dict) -> bool:
        """
        保存或更新预测记录（核心方法）
        如果记录存在就更新，不存在就新增
        
        参数:
            data: 预测数据字典
        返回:
            bool: 是否操作成功
        """
        try:
            
            # 检查记录是否已存在
            existing = self.session.query(PredictionHistory).filter_by(
                date=data.get("Date"),
                symbol=data.get("Symbol")
            ).first()
            
            if existing:
                # 更新现有记录
                self._update_record_fields(existing, data)
                existing.updated_at = datetime.utcnow()
                self.logger.info(f"更新预测记录: {data.get('Date')} {data.get('Symbol')}")
            else:
                # 创建新记录
                new_record = PredictionHistory.from_dict(data)
                self.session.add(new_record)
                self.logger.info(f"创建预测记录: {data.get('Date')} {data.get('Symbol')}")
            
            self.session.commit()
            return True
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"保存或更新预测记录失败: {e}")
            return False

    def batch_save_or_update(self, data_list: List[Dict]) -> bool:
        """
        批量保存或更新预测记录
        
        参数:
            data_list: 预测数据字典列表
        返回:
            bool: 是否操作成功
        """
        try:
            success_count = 0
            total_count = len(data_list)
            
            for data in data_list:
                if self.save_or_update(data):
                    success_count += 1
            
            self.logger.info(f"批量操作完成: {success_count}/{total_count} 成功")
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"批量保存或更新失败: {e}")
            return False

    def list_all(self, symbol: Optional[str] = None, limit: Optional[int] = None) -> List[Dict]:
        """
        获取所有预测记录
        
        参数:
            symbol: 交易对符号，None表示获取所有
            limit: 限制返回数量
        返回:
            List[Dict]: 预测记录列表
        """
        try:
            query = self.session.query(PredictionHistory)
            
            if symbol:
                query = query.filter(PredictionHistory.symbol == symbol)
            
            # 按日期降序排列
            query = query.order_by(PredictionHistory.date.desc())
            
            if limit:
                query = query.limit(limit)
            
            results = query.all()
            return [record.to_dict() for record in results]
            
        except Exception as e:
            self.logger.error(f"获取预测记录列表失败: {e}")
            return []

    def exists(self, date: str, symbol: str = "EURUSD") -> bool:
        """
        检查预测记录是否存在
        
        参数:
            date: 日期字符串 (YYYY-MM-DD)
            symbol: 交易对符号
        返回:
            bool: 记录是否存在
        """
        try:
            exists = self.session.query(PredictionHistory).filter_by(
                date=date,
                symbol=symbol
            ).first() is not None
            
            return exists
            
        except Exception as e:
            self.logger.error(f"检查记录存在性失败: {e}")
            return False

    def get_prediction_record(self, symbol: str, date: str) -> Optional[PredictionHistory]:
        """
        获取预测记录对象（用于更新AI报告）
        
        参数:
            symbol: 交易对符号
            date: 日期字符串 (YYYY-MM-DD)
        返回:
            Optional[PredictionHistory]: 预测记录对象或None
        """
        try:
            record = self.session.query(PredictionHistory).filter_by(
                date=date,
                symbol=symbol
            ).first()
            
            return record
            
        except Exception as e:
            self.logger.error(f"获取预测记录对象失败: {e}")
            return None

    def _update_record_fields(self, record: PredictionHistory, data: Dict):
        """更新记录字段"""
        # 更新预测相关字段
        if "09:00 Price" in data and data["09:00 Price"]:
            record.morning_price = float(data["09:00 Price"])
        
        if "Predicted Direction" in data:
            record.predicted_direction = data["Predicted Direction"]
        
        if "Predicted 16:00 Price" in data and data["Predicted 16:00 Price"]:
            record.predicted_price = float(data["Predicted 16:00 Price"])
        
        if "Predicted Magnitude (%)" in data and data["Predicted Magnitude (%)"]:
            record.predicted_magnitude = float(data["Predicted Magnitude (%)"])
        
        if "Price Difference" in data and data["Price Difference"]:
            record.price_difference = float(data["Price Difference"])
        
        # 更新实际结果字段
        actual_price = data.get("Actual 16:00 Price")
        if actual_price and actual_price != "N/A":
            record.actual_price = float(actual_price)
        
        actual_direction = data.get("Actual Direction")
        if actual_direction and actual_direction != "N/A":
            record.actual_direction = actual_direction
        
        price_diff = data.get("Price Difference (Actual-Predicted)")
        if price_diff and price_diff != "N/A":
            record.price_diff_actual_predicted = float(price_diff)
        
        deviation = data.get("Deviation (%)")
        if deviation and deviation != "N/A":
            record.deviation_percentage = float(deviation)
        
        # 更新AI报告
        ai_report = data.get("AI_Report")
        if ai_report and isinstance(ai_report, dict):
            record.ai_report_status = ai_report.get("status")
            record.ai_report_content = ai_report.get("report")
            record.ai_report_error = ai_report.get("error")

    def close(self):
        """关闭数据库会话"""
        self.session.close()


# 全局预测历史服务实例
prediction_history_service = PredictionHistoryService()
