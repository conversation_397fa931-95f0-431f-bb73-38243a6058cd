"""
数据库服务模块
"""

from datetime import datetime
import pandas as pd
from app.models.bar_data import BarData, Session
from typing import List, Dict, Optional
from app.logger_config import get_module_logger


class BarDataService:
    """数据库服务类"""

    def __init__(self):
        self.session = Session()
        self.logger = get_module_logger("bar_data_service")

    def save_bar_data(self, data: List[Dict], symbol: str) -> None:
        """
        保存市场数据到数据库
        :param data: 市场数据列表
        :param symbol: 交易对符号
        """
        try:
            # 使用批量操作提高性能
            from sqlalchemy.dialects.sqlite import insert

            # 准备数据
            records_to_insert = []
            for item in data:
                record = {
                    "time": item["time"],
                    "symbol": symbol,
                    "open": float(item["open"]),
                    "high": float(item["high"]),
                    "low": float(item["low"]),
                    "close": float(item["close"]),
                    "volume": int(item.get("volume", 0)),
                }
                records_to_insert.append(record)

            if records_to_insert:
                # 使用 SQLite 的 INSERT OR REPLACE 语法
                stmt = insert(BarData).values(records_to_insert)
                stmt = stmt.on_conflict_do_update(
                    index_elements=["time", "symbol"],
                    set_=dict(
                        open=stmt.excluded.open,
                        high=stmt.excluded.high,
                        low=stmt.excluded.low,
                        close=stmt.excluded.close,
                        volume=stmt.excluded.volume,
                    ),
                )
                self.session.execute(stmt)
                self.session.commit()
                self.logger.info(
                    f"成功保存/更新 {len(records_to_insert)} 条 {symbol} 数据"
                )

        except Exception as e:
            self.session.rollback()
            self.logger.error(f"保存数据时发生错误: {e}")
            # 如果批量操作失败，回退到逐条处理
            try:
                for item in data:
                    # 创建市场数据对象
                    bar_data = BarData(
                        time=item["time"],
                        symbol=symbol,
                        open=float(item["open"]),
                        high=float(item["high"]),
                        low=float(item["low"]),
                        close=float(item["close"]),
                        volume=int(item.get("volume", 0)),
                    )

                    # 如果记录已存在，则更新
                    existing = (
                        self.session.query(BarData)
                        .filter_by(time=item["time"], symbol=symbol)
                        .first()
                    )

                    if existing:
                        existing.open = bar_data.open
                        existing.high = bar_data.high
                        existing.low = bar_data.low
                        existing.close = bar_data.close
                        existing.volume = bar_data.volume
                    else:
                        self.session.add(bar_data)

                self.session.commit()
                self.logger.info(
                    f"使用逐条处理方式成功保存 {len(data)} 条 {symbol} 数据"
                )
            except Exception as fallback_error:
                self.session.rollback()
                raise fallback_error

    def load_bar_data(
        self,
        symbol: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
    ) -> pd.DataFrame:
        """
        从数据库加载市场数据
        :param symbol: 交易对符号
        :param start_time: 开始时间
        :param end_time: 结束时间
        :return: 包含市场数据的DataFrame
        """
        query = self.session.query(BarData).filter(BarData.symbol == symbol)

        if start_time:
            query = query.filter(BarData.time >= start_time)
        if end_time:
            query = query.filter(BarData.time <= end_time)

        # 按时间排序
        query = query.order_by(BarData.time)

        # 转换为DataFrame
        results = query.all()
        data = [record.to_dict() for record in results]
        df = pd.DataFrame(data)
        df = self.covert_data(df)
        return df

    def get_latest_data(self, symbol: str, limit: int = 1) -> pd.DataFrame:
        """
        获取最新的市场数据
        :param symbol: 交易对符号
        :param limit: 返回的记录数量
        :return: 包含最新市场数据的DataFrame
        """
        query = (
            self.session.query(BarData)
            .filter(BarData.symbol == symbol)
            .order_by(BarData.time.desc())
            .limit(limit)
        )

        results = query.all()
        data = [record.to_dict() for record in results]
        df = pd.DataFrame(data)
        df = self.covert_data(df)
        return df

    def get_symbols(self) -> List[str]:
        """
        获取所有可用的交易对符号
        :return: 交易对符号列表
        """
        return [symbol[0] for symbol in self.session.query(BarData.symbol).distinct()]

    def covert_data(self, data) -> pd.DataFrame:
        # 转换数据格式
        self.logger.debug(f"原始列名: {data.columns.tolist()}")

        # 重命名列以匹配原有逻辑
        data = data.rename(
            columns={
                "time": "datetime",
                "open": "Open",
                "high": "High",
                "low": "Low",
                "close": "Close",
                "volume": "Volume",
            }
        )

        self.logger.debug(f"重命名后的列名: {data.columns.tolist()}")

        if "datetime" not in data.columns:
            self.logger.error(
                "未找到datetime列，当前可用的列: " + ", ".join(data.columns.tolist())
            )
            raise ValueError("数据格式错误：缺少datetime列")

        # 添加Date列（从datetime中提取日期，转换为字符串格式）
        data["Date"] = data["datetime"].dt.strftime("%Y-%m-%d").astype(str)
        data["Time"] = data["datetime"].dt.strftime("%H:%M:%S").astype(str)

        # 只保留指定的列
        required_columns = ["Date", "Time", "Open", "High", "Low", "Close", "Volume"]
        data = data[required_columns]

        self.logger.debug(data)
        return data

    def close(self):
        """关闭数据库会话"""
        self.session.close()


bar_data_service = BarDataService()
