import asyncio
from datetime import datetime, timezone

import pandas as pd
from app.core.fetch_data import crawl_all_data
from app.config import SYMBOLS, PKL_DIR
from app.core import prediction_service
from app.crud import prediction_history_service, bar_data_service
from app.core import scheduler_service


def generate_history():
    for symbol in SYMBOLS:
        data = bar_data_service.load_bar_data(symbol, end_time=datetime.now())
        scheduler_service.train_and_save_model(symbol)
        results = prediction_service.predict(
            data,
            f"{PKL_DIR}/{symbol}/{symbol}_1H_SVM_{datetime.now().strftime('%Y%m%d')}.pkl",
            days=50,
        )
        for result in results:
            result["Symbol"] = symbol
            prediction_history_service.save_or_update(result)
        predictions = prediction_history_service.list_all(symbol)
        data["Datetime"] = pd.to_datetime(data["Date"] + " " + data["Time"])
        data["datetime"] = data.apply(lambda x: x["Date"] + " " + x["Time"], axis=1)
        data["datetime"] = pd.to_datetime(data["datetime"])
        for pred in predictions:
            current_date = pd.to_datetime(pred["Date"]).date()
            actual_close = data[
                (data["datetime"].dt.date == current_date)
                & (data["datetime"].dt.hour == 7)  # 修改到 7 ，是16点的开盘价
            ]["Close"]

            if not actual_close.empty:
                actual_close_price = actual_close.iloc[-1]
                pred["Actual 16:00 Price"] = actual_close_price

                # 计算预测值和实际值的价差
                predicted_close_price = pred["Predicted 16:00 Price"]
                price_diff = actual_close_price - predicted_close_price
                pred["Price Difference (Actual-Predicted)"] = price_diff

                # 计算偏离度
                deviation = (price_diff / predicted_close_price) * 100
                pred["Deviation (%)"] = deviation

                # 判断预测方向是否正确
                predicted_direction = pred["Predicted Direction"]
                actual_direction = (
                    "Up" if actual_close_price > pred["09:00 Price"] else "Down"
                )
                pred["Actual Direction"] = actual_direction
            else:
                if pred["Actual 16:00 Price"] is None:
                    pred["Actual 16:00 Price"] = "N/A"
                    pred["Price Difference (Actual-Predicted)"] = "N/A"
                    pred["Deviation (%)"] = "N/A"
                    pred["Actual Direction"] = "N/A"
            prediction_history_service.save_or_update(pred)


async def fetch_data():
    target_timestamp = int(datetime(2021, 1, 1, tzinfo=timezone.utc).timestamp() * 1000)
    tasks = []
    for symbol in SYMBOLS:
        task = asyncio.create_task(crawl_all_data(symbol, target_timestamp))
        tasks.append(task)

    # 等待所有爬虫任务完成
    await asyncio.gather(*tasks)

    # 所有任务完成后生成历史数据
    generate_history()


# 运行主函数
if __name__ == "__main__":
    asyncio.run(fetch_data())
