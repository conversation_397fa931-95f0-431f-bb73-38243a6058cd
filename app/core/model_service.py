from datetime import date, datetime

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVR
from sklearn.metrics import mean_squared_error, mean_absolute_error
import joblib
import os
from app.logger_config import get_module_logger


class ModelService:
    """
    模型服务类，提供交易预测模型的创建和训练功能
    """
    def __init__(self):
        self.logger = get_module_logger("model_service")
    def load_data(self, file_path):
        """
        读取CSV数据并处理格式
        :param file_path: CSV文件路径
        :return: pandas DataFrame
        """
        data = pd.read_csv(
            file_path,
            header=None,
            names=["Date", "Time", "Open", "High", "Low", "Close", "Volume"],
        )
        data["Datetime"] = pd.to_datetime(data["Date"] + " " + data["Time"])
        data["datetime"] = data.apply(lambda x: x["Date"] + " " + x["Time"], axis=1)
        data["datetime"] = pd.to_datetime(data["datetime"])
        data["pct_chg"] = data["Close"].pct_change()
        return data

    def add_indicators(self, data):
        """
        添加技术指标
        :param data: 原始DataFrame
        :return: 包含技术指标的DataFrame
        """
        data["pct_chg"] = data["Close"].pct_change()
        data["MA20"] = data["Close"].rolling(window=20).mean()
        data["RSI"] = self.compute_rsi(data["Close"], window=14)
        data["MACD"], data["Signal"], _ = self.compute_macd(data["Close"])
        data["ATR"] = self.compute_atr(
            data["High"], data["Low"], data["Close"], window=14
        )
        data["7H_Mean_Change"] = data["Close"].rolling(window=7).mean() - data["Close"]
        data["7H_Volatility"] = data["Close"].rolling(window=7).std()
        data["MA5-MA20"] = (
            data["Close"].rolling(window=5).mean()
            - data["Close"].rolling(window=20).mean()
        )
        data["ADX"] = self.compute_adx(
            data["High"], data["Low"], data["Close"], window=14
        )
        data.dropna(inplace=True)
        return data

    def compute_rsi(self, series, window=14):
        """计算RSI指标"""
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def compute_macd(self, series, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        ema_fast = series.ewm(span=fast, min_periods=1).mean()
        ema_slow = series.ewm(span=slow, min_periods=1).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal, min_periods=1).mean()
        return macd, signal_line, macd - signal_line

    def compute_atr(self, high, low, close, window=14):
        """计算ATR指标"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window).mean()

    def compute_adx(self, high, low, close, window=14):
        """计算ADX指标"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        plus_dm = high.diff()
        minus_dm = low.diff()

        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm > 0] = 0

        atr = tr.rolling(window).mean()
        plus_di = 100 * (plus_dm.rolling(window).mean() / atr)
        minus_di = 100 * (minus_dm.abs().rolling(window).mean() / atr)

        dx = 100 * (abs(plus_di - minus_di) / (plus_di + minus_di))
        adx = dx.rolling(window).mean()
        return adx

    def add_shifted_columns(self, data, columns, lags):
        """
        为指定列添加滞后特征
        :param data: 原始DataFrame
        :param columns: 需要添加滞后的列名列表
        :param lags: 滞后期数范围
        :return: 包含滞后特征的DataFrame
        """
        shifted_data = []
        for t_i in lags:
            shifted = data[columns].shift(t_i).add_suffix(f"_t{t_i}")
            shifted_data.append(shifted)
        return pd.concat([data] + shifted_data, axis=1)

    def filter_and_group_data(
        self, data, start_date="2021-01-01", start_hour=0, end_hour=7
    ):
        """
        过滤和分组数据
        :param data: 原始DataFrame
        :param start_date: 开始日期
        :param start_hour: 开始小时
        :param end_hour: 结束小时
        :return: 处理后的DataFrame
        """
        # 过滤日期
        data = data.query(f"Date>='{start_date}'")

        # 过滤时间范围
        df_filtered = data[
            (data["datetime"].dt.hour >= start_hour)
            & (data["datetime"].dt.hour < end_hour)
        ]

        # 定义聚合字典
        columns_to_shift = [
            "pct_chg",
            "MA20",
            "RSI",
            "MACD",
            "Signal",
            "ATR",
            "7H_Mean_Change",
            "7H_Volatility",
            "MA5-MA20",
            "ADX",
        ]
        lags = range(0, 11)

        agg_dict = {
            "datetime": "first",
            "Open": "first",
            "High": "max",
            "Low": "min",
            "Close": "last",
            "Volume": "sum",
        }

        # 为滞后列添加聚合规则
        for col in columns_to_shift:
            for t_i in lags:
                agg_dict[f"{col}_t{t_i}"] = "first"

        # 按日期分组并聚合
        df_grouped = (
            df_filtered.groupby(df_filtered["datetime"].dt.date)
            .agg(agg_dict)
            .reset_index(drop=True)
        )

        # 计算日收益率和下一日收益率
        df_grouped["pct_chg"] = (
            (df_grouped["Close"] - df_grouped["Open"]) / df_grouped["Open"] * 100
        )
        df_grouped["next_pct_chg"] = df_grouped["pct_chg"].shift(-1)
        df_grouped = df_grouped.dropna()

        # 去碎片化
        df_grouped = df_grouped.copy()

        return df_grouped

    def get_required_columns(self):
        """
        获取模型训练所需的特征列名
        :return: 特征列名列表
        """
        return [
            "pct_chg_t0",
            "MA20_t0",
            "RSI_t0",
            "MACD_t0",
            "Signal_t0",
            "ATR_t0",
            "7H_Mean_Change_t0",
            "7H_Volatility_t0",
            "MA5-MA20_t0",
            "ADX_t0",
            "pct_chg_t1",
            "MA20_t1",
            "RSI_t1",
            "MACD_t1",
            "Signal_t1",
            "ATR_t1",
            "7H_Mean_Change_t1",
            "7H_Volatility_t1",
            "MA5-MA20_t1",
            "ADX_t1",
            "pct_chg_t2",
            "MA20_t2",
            "RSI_t2",
            "MACD_t2",
            "Signal_t2",
            "ATR_t2",
            "7H_Mean_Change_t2",
            "7H_Volatility_t2",
            "MA5-MA20_t2",
            "ADX_t2",
            "pct_chg_t3",
            "MA20_t3",
            "RSI_t3",
            "MACD_t3",
            "Signal_t3",
            "ATR_t3",
            "7H_Mean_Change_t3",
            "7H_Volatility_t3",
            "MA5-MA20_t3",
            "ADX_t3",
            "pct_chg_t4",
            "MA20_t4",
            "RSI_t4",
            "MACD_t4",
            "Signal_t4",
            "ATR_t4",
            "7H_Mean_Change_t4",
            "7H_Volatility_t4",
            "MA5-MA20_t4",
            "ADX_t4",
            "pct_chg_t5",
            "MA20_t5",
            "RSI_t5",
            "MACD_t5",
            "Signal_t5",
            "ATR_t5",
            "7H_Mean_Change_t5",
            "7H_Volatility_t5",
            "MA5-MA20_t5",
            "ADX_t5",
            "pct_chg_t6",
            "MA20_t6",
            "RSI_t6",
            "MACD_t6",
            "Signal_t6",
            "ATR_t6",
            "7H_Mean_Change_t6",
            "7H_Volatility_t6",
            "MA5-MA20_t6",
            "ADX_t6",
            "pct_chg_t7",
            "MA20_t7",
            "RSI_t7",
            "MACD_t7",
            "Signal_t7",
            "ATR_t7",
            "7H_Mean_Change_t7",
            "7H_Volatility_t7",
            "MA5-MA20_t7",
            "ADX_t7",
            "pct_chg_t8",
            "MA20_t8",
            "RSI_t8",
            "MACD_t8",
            "Signal_t8",
            "ATR_t8",
            "7H_Mean_Change_t8",
            "7H_Volatility_t8",
            "MA5-MA20_t8",
            "ADX_t8",
            "pct_chg_t9",
            "MA20_t9",
            "RSI_t9",
            "MACD_t9",
            "Signal_t9",
            "ATR_t9",
            "7H_Mean_Change_t9",
            "7H_Volatility_t9",
            "MA5-MA20_t9",
            "ADX_t9",
            "pct_chg_t10",
            "MA20_t10",
            "RSI_t10",
            "MACD_t10",
            "Signal_t10",
            "ATR_t10",
            "7H_Mean_Change_t10",
            "7H_Volatility_t10",
            "MA5-MA20_t10",
            "ADX_t10",
        ]

    def train_models(self, df_grouped, svm_model_path):
        """
        训练SVR模型
        :param df_grouped: 包含特征和目标变量的DataFrame
        :param svm_model_path: 模型保存路径
        :return: 训练好的模型和评估指标
        """
        required_columns = self.get_required_columns()

        # 特征选择
        features = df_grouped[required_columns]
        X = features
        y = df_grouped["next_pct_chg"]

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.1, random_state=42
        )

        # 特征缩放
        scaler = StandardScaler()
        X_train = scaler.fit_transform(X_train)
        X_test = scaler.transform(X_test)

        # 训练 SVR 模型
        model = SVR(kernel="rbf", C=1, epsilon=0.1)  # 使用 RBF 核函数
        model.fit(X_train, y_train)

        # 生成模型评估报告
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)

        # 计算胜率
        y_pred_direction = np.sign(y_pred)
        y_test_direction = np.sign(y_test)
        win_rate = np.mean(y_pred_direction == y_test_direction) * 100

        self.logger.info("模型评估报告:")
        self.logger.info(f"均方误差 (MSE): {mse:.4f}")
        self.logger.info(f"平均绝对误差 (MAE): {mae:.4f}")
        self.logger.info(f"胜率 (Win Rate): {win_rate:.2f}%")

        # 保存训练好的模型
        joblib.dump(model, svm_model_path)

        return model, {"mse": mse, "mae": mae, "win_rate": win_rate}

    def create_model(self, symbol, data, model_save_dir):
        """
        创建并训练模型的主函数
        :param symbol: 交易品种符号 (如 'XAUUSD', 'EURUSD' 等)
        :param data: 输入的DataFrame数据，应包含 Date, Time, Open, High, Low, Close, Volume 列
        :param model_save_dir: 模型保存目录
        :return: 训练好的模型和评估指标
        """
        self.logger.info(f"开始为 {symbol} 创建模型...")

        # 确保模型保存目录存在
        os.makedirs(model_save_dir, exist_ok=True)

        # 数据预处理
        self.logger.info("1. 添加技术指标...")
        data = self.add_indicators(data)

        # 添加滞后特征
        self.logger.info("2. 添加滞后特征...")
        columns_to_shift = [
            "pct_chg",
            "MA20",
            "RSI",
            "MACD",
            "Signal",
            "ATR",
            "7H_Mean_Change",
            "7H_Volatility",
            "MA5-MA20",
            "ADX",
        ]
        lags = range(0, 11)
        data = self.add_shifted_columns(data, columns_to_shift, lags)

        # 过滤和分组数据
        self.logger.info("3. 过滤和分组数据...")
        df_grouped = self.filter_and_group_data(data)

        self.logger.info(f"4. 数据准备完成，共 {len(df_grouped)} 条记录")

        # 训练模型
        self.logger.info("5. 开始训练模型...")
        model_path = os.path.join(model_save_dir, f"{symbol}_1H_SVM_{datetime.now().strftime("%Y%m%d")}.pkl")
        model, metrics = self.train_models(df_grouped, model_path)

        self.logger.info(f"6. 模型训练完成，已保存到: {model_path}")
        self.logger.info(
            f"模型评估结果: MSE={metrics['mse']:.4f}, MAE={metrics['mae']:.4f}, 胜率={metrics['win_rate']:.2f}%"
        )


model_service = ModelService()