import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
import joblib
import json
import os
from io import String<PERSON>
from typing import List, Dict, Any, Optional
from app.crud.prediction_history_service import prediction_history_service
from app.logger_config import get_module_logger


class PredictionService:
    """
    预测服务类，提供交易品种的价格预测功能
    """

    def __init__(self):
        self.logger = get_module_logger("prediction_service")

    def _load_model(self, model_path: str):
        """加载SVM模型"""
        try:
            model = joblib.load(model_path)
            self.logger.info("SVM model loaded successfully.")
            return model
        except Exception as e:
            self.logger.error(f"Error loading SVM model: {e}")
            raise

    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成预测所需的技术指标和新特征
        :param data: 原始数据
        :return: 生成了所有模型所需特征的数据
        """
        data["MA20"] = data["Close"].rolling(window=20).mean()
        data["RSI"] = self.compute_rsi(data["Close"], window=14)
        data["MACD"], data["Signal"], _ = self.compute_macd(data["Close"])
        data["ATR"] = self.compute_atr(
            data["High"], data["Low"], data["Close"], window=14
        )
        data["7H_Mean_Change"] = data["Close"].rolling(window=7).mean() - data["Close"]
        data["7H_Volatility"] = data["Close"].rolling(window=7).std()
        data["MA5-MA20"] = (
            data["Close"].rolling(window=5).mean()
            - data["Close"].rolling(window=20).mean()
        )
        data["ADX"] = self.compute_adx(
            data["High"], data["Low"], data["Close"], window=14
        )
        data.dropna(inplace=True)  # 删除NaN行
        return data

    def compute_rsi(self, series: pd.Series, window: int = 14) -> pd.Series:
        """计算 RSI 指标"""
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def compute_macd(
        self, series: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9
    ):
        """计算 MACD 指标"""
        ema_fast = series.ewm(span=fast, min_periods=1).mean()
        ema_slow = series.ewm(span=slow, min_periods=1).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal, min_periods=1).mean()
        return macd, signal_line, macd - signal_line

    def compute_atr(
        self, high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14
    ) -> pd.Series:
        """计算 ATR 指标"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window).mean()

    def compute_adx(
        self, high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14
    ) -> pd.Series:
        """计算ADX指标"""
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        plus_dm = high.diff()
        minus_dm = low.diff()

        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm > 0] = 0

        atr = tr.rolling(window).mean()
        plus_di = 100 * (plus_dm.rolling(window).mean() / atr)
        minus_di = 100 * (minus_dm.abs().rolling(window).mean() / atr)

        dx = 100 * (abs(plus_di - minus_di) / (plus_di + minus_di))
        adx = dx.rolling(window).mean()
        return adx

    def update_actual_prices(self, data_file: str, prediction_file: str) -> None:
        """
        补充实际的 16:00 收盘价格到预测结果中，计算价差、偏离度和预测胜率。
        """
        with open(prediction_file, "r", encoding="utf-8") as f:
            predictions = json.load(f)

        if not isinstance(predictions, list) or not all(
            isinstance(p, dict) for p in predictions
        ):
            raise TypeError("Predictions data must be a list containing dictionaries.")

        data = pd.read_csv(data_file)
        data.columns = ["Date", "Time", "Open", "High", "Low", "Close", "Volume"]
        data["datetime"] = pd.to_datetime(data["Date"] + " " + data["Time"])

        total_predictions = 0
        correct_predictions = 0

        # 遍历预测条目
        for pred in predictions:
            if "Date" not in pred:
                raise KeyError("Each prediction entry must contain a 'Date' key.")

            current_date = pd.to_datetime(pred["Date"]).date()

            # 查找当前日期 16:00 的收盘价
            actual_close = data[
                (data["datetime"].dt.date == current_date)
                & (data["datetime"].dt.hour == 7)  # 修改到 7 ，是16点的开盘价
            ]["Close"]

            if not actual_close.empty:
                actual_close_price = actual_close.iloc[-1]
                pred["Actual 16:00 Price"] = actual_close_price

                # 计算预测值和实际值的价差
                predicted_close_price = pred["Predicted 16:00 Price"]
                price_diff = (actual_close_price - predicted_close_price,)
                pred["Price Difference (Actual-Predicted)"] = price_diff

                # 计算偏离度
                deviation = (price_diff / predicted_close_price) * 100
                pred["Deviation (%)"] = deviation

                # 判断预测方向是否正确
                predicted_direction = pred["Predicted Direction"]
                actual_direction = (
                    "Up" if actual_close_price > pred["09:00 Price"] else "Down"
                )
                pred["Actual Direction"] = actual_direction

                if predicted_direction == actual_direction:
                    correct_predictions += 1
                total_predictions += 1
            else:
                if pred["Actual 16:00 Price"] is None:
                    pred["Actual 16:00 Price"] = "N/A"
                    pred["Price Difference (Actual-Predicted)"] = "N/A"
                    pred["Deviation (%)"] = "N/A"
                    pred["Actual Direction"] = "N/A"

        # 统计预测胜率
        accuracy = (
            round((correct_predictions / total_predictions) * 100, 2)
            if total_predictions > 0
            else 0
        )
        self.logger.info(f"Total Predictions: {total_predictions}")
        self.logger.info(f"Correct Predictions: {correct_predictions}")
        self.logger.info(f"Prediction Accuracy: {accuracy}%")

        # 保存更新后的预测结果
        with open(prediction_file, "w", encoding="utf-8") as f:
            json.dump(predictions, f, ensure_ascii=False, indent=4)

        self.logger.info(f"已更新实际值，结果保存在 {prediction_file}")

    def predict(
        self,
        data: pd.DataFrame,
        model_path: str,
        days: int = 50,
    ) -> List[Dict[str, Any]]:
        """
        执行预测功能
        :param symbol: 交易品种符号
        :param data: 输入的DataFrame数据，应包含 Date, Time, Open, High, Low, Close, Volume 列
        :param model_path: SVM模型文件路径
        :param history_file: 历史记录文件路径
        :param days: 预测最后多少天，默认为50天
        :return: 最新的预测结果
        """
        # 加载模型

        self.svm_model = self._load_model(model_path)
        # 数据预处理
        data.columns = ["Date", "Time", "Open", "High", "Low", "Close", "Volume"]
        data["Date"] = pd.to_datetime(data["Date"]).apply(
            lambda x: x.strftime("%Y-%m-%d")
        )
        data["datetime"] = pd.to_datetime(data["Date"] + " " + data["Time"])
        data["pct_chg"] = data["Close"].pct_change()

        data = self.preprocess_data(data)
        agg_dict = {
            "datetime": "first",
            "Open": "first",
            "High": "max",
            "Low": "min",
            "Close": "last",
            "Volume": "sum",
        }
        # 构造滞后特征 - 使用 pd.concat 优化性能
        lag_columns = [
            "pct_chg",
            "MA20",
            "RSI",
            "MACD",
            "Signal",
            "ATR",
            "7H_Mean_Change",
            "7H_Volatility",
            "MA5-MA20",
            "ADX",
        ]

        # 创建所有滞后特征的 DataFrame 列表
        lag_dataframes = []
        for t_i in range(0, 11):
            for col in lag_columns:
                lag_df = data[col].shift(t_i).to_frame(f"{col}_t{t_i}")
                lag_dataframes.append(lag_df)
                agg_dict[f"{col}_t{t_i}"] = "first"
        # 一次性合并所有滞后特征
        if lag_dataframes:
            lag_features = pd.concat(lag_dataframes, axis=1)
            data = pd.concat([data, lag_features], axis=1)
        # 过滤交易时间并初始化结果列表
        df_filtered = data[
            (data["datetime"].dt.hour >= 0) & (data["datetime"].dt.hour < 7)
        ]  # 修改为 7 ， 是 16 点的开盘价

        df_grouped = (
            df_filtered.groupby(df_filtered["datetime"].dt.date)
            .agg(agg_dict)
            .reset_index(drop=True)
        )
        df_grouped["pct_chg"] = (
            (df_grouped["Close"] - df_grouped["Open"]) / df_grouped["Open"] * 100
        )
        df_grouped.dropna(inplace=True)

        required_columns = [
            "pct_chg_t0",
            "MA20_t0",
            "RSI_t0",
            "MACD_t0",
            "Signal_t0",
            "ATR_t0",
            "7H_Mean_Change_t0",
            "7H_Volatility_t0",
            "MA5-MA20_t0",
            "ADX_t0",
            "pct_chg_t1",
            "MA20_t1",
            "RSI_t1",
            "MACD_t1",
            "Signal_t1",
            "ATR_t1",
            "7H_Mean_Change_t1",
            "7H_Volatility_t1",
            "MA5-MA20_t1",
            "ADX_t1",
            "pct_chg_t2",
            "MA20_t2",
            "RSI_t2",
            "MACD_t2",
            "Signal_t2",
            "ATR_t2",
            "7H_Mean_Change_t2",
            "7H_Volatility_t2",
            "MA5-MA20_t2",
            "ADX_t2",
            "pct_chg_t3",
            "MA20_t3",
            "RSI_t3",
            "MACD_t3",
            "Signal_t3",
            "ATR_t3",
            "7H_Mean_Change_t3",
            "7H_Volatility_t3",
            "MA5-MA20_t3",
            "ADX_t3",
            "pct_chg_t4",
            "MA20_t4",
            "RSI_t4",
            "MACD_t4",
            "Signal_t4",
            "ATR_t4",
            "7H_Mean_Change_t4",
            "7H_Volatility_t4",
            "MA5-MA20_t4",
            "ADX_t4",
            "pct_chg_t5",
            "MA20_t5",
            "RSI_t5",
            "MACD_t5",
            "Signal_t5",
            "ATR_t5",
            "7H_Mean_Change_t5",
            "7H_Volatility_t5",
            "MA5-MA20_t5",
            "ADX_t5",
            "pct_chg_t6",
            "MA20_t6",
            "RSI_t6",
            "MACD_t6",
            "Signal_t6",
            "ATR_t6",
            "7H_Mean_Change_t6",
            "7H_Volatility_t6",
            "MA5-MA20_t6",
            "ADX_t6",
            "pct_chg_t7",
            "MA20_t7",
            "RSI_t7",
            "MACD_t7",
            "Signal_t7",
            "ATR_t7",
            "7H_Mean_Change_t7",
            "7H_Volatility_t7",
            "MA5-MA20_t7",
            "ADX_t7",
            "pct_chg_t8",
            "MA20_t8",
            "RSI_t8",
            "MACD_t8",
            "Signal_t8",
            "ATR_t8",
            "7H_Mean_Change_t8",
            "7H_Volatility_t8",
            "MA5-MA20_t8",
            "ADX_t8",
            "pct_chg_t9",
            "MA20_t9",
            "RSI_t9",
            "MACD_t9",
            "Signal_t9",
            "ATR_t9",
            "7H_Mean_Change_t9",
            "7H_Volatility_t9",
            "MA5-MA20_t9",
            "ADX_t9",
            "pct_chg_t10",
            "MA20_t10",
            "RSI_t10",
            "MACD_t10",
            "Signal_t10",
            "ATR_t10",
            "7H_Mean_Change_t10",
            "7H_Volatility_t10",
            "MA5-MA20_t10",
            "ADX_t10",
        ]
        features = df_grouped[required_columns]

        scaler = StandardScaler()
        X_test = scaler.fit_transform(features)

        svm_pred = self.svm_model.predict(X_test)
        df_grouped["svm_pred"] = svm_pred
        results = []
        history_date = df_filtered["datetime"].dt.date.unique()[-days:]
        for current_date in history_date:
            current_datetime = pd.to_datetime(str(current_date) + " " + "00:00:00")
            current_data = data[data["datetime"] < current_datetime]
            current_data_grouped = df_grouped[
                df_grouped["datetime"].dt.date < current_date
            ]
            if current_data_grouped.empty:
                continue
            svm_pred = current_data_grouped.iloc[-1]["svm_pred"]
            predicted_price_16 = current_data["Close"].iloc[-1] * (1 + (svm_pred / 100))
            price_diff = predicted_price_16 - current_data["Close"].iloc[-1]

            # actual_close_price = (
            #     df_filtered[
            #         (df_filtered["datetime"].dt.date == current_date)
            #         & (
            #             df_filtered["datetime"].dt.hour == 7
            #         )  # 修改为 7 ， 是 16 点的开盘价
            #     ]["Close"].iloc[-1]
            #     if not df_filtered[
            #         (df_filtered["datetime"].dt.date == current_date)
            #         & (
            #             df_filtered["datetime"].dt.hour == 7
            #         )  # 修改为 7 ， 是 16 点的开盘价
            #     ].empty
            #     else None
            # )

            result = {
                "Date": current_date.strftime("%Y-%m-%d"),
                "09:00 Price": current_data["Close"].iloc[-1],
                "Predicted Direction": "Up" if svm_pred >= 0 else "Down",
                "Predicted 16:00 Price": predicted_price_16,
                # "Actual 16:00 Price": (
                #     round(actual_close_price, 2)
                #     if actual_close_price is not None
                #     else "N/A"
                # ),
                "Actual 16:00 Price": "N/A",
                "Price Difference": price_diff,
                "Predicted Magnitude (%)": round(svm_pred, 4),
            }
            results.append(result)
        return results


prediction_service = PredictionService()
