"""
AI服务模块
"""
from openai import OpenAI
# from volcenginesdkarkruntime import Ark
from app.config import DEEPSEEK_V3_KEY, DEEPSEEK_URL, DEEPSEEK_MODEL
from app.logger_config import get_module_logger


class AIService:
    """AI服务"""

    def __init__(self):
        # self.client = Ark(
        #     base_url=DEEPSEEK_URL,
        #     api_key=DEEPSEEK_V3_KEY,
        # )
        self.client = OpenAI(
            base_url=DEEPSEEK_URL,
            api_key=DEEPSEEK_V3_KEY,
        )
        self.logger = get_module_logger("ai_service")


    def stream_large_model(self, prompt_data):
        """
        使用真实大模型接口进行流式调用，返回分析报告
        """
        # 构建提示词
        prompt = f"""
        请基于以下黄金价格预测数据，生成一份详细的分析报告：
        
        日期: {prompt_data['Date']}
        09:00价格: {prompt_data['09:00 Price']}
        预测方向: {prompt_data['Predicted Direction']}
        预测16:00价格: {prompt_data['Predicted 16:00 Price']}
        实际16:00价格: {prompt_data['Actual 16:00 Price'] if prompt_data['Actual 16:00 Price'] != 'N/A' else '暂无'}
        价格差异: {prompt_data['Price Difference']}
        预测幅度(%): {prompt_data['Predicted Magnitude (%)']}
        
        请分析以下内容：
        1. 预测结果的可靠性分析
        2. 影响黄金价格的可能因素
        3. 对投资者的建议
        4. 未来市场趋势展望
        
        请以专业金融分析师的口吻撰写，报告应该简洁明了但内容全面。
        请使用Markdown格式输出，包括标题、列表、表格等，以便更好地展示内容。
        """

        role = "你是一位专业的金融分析师，擅长分析黄金市场。请使用Markdown格式输出你的分析报告。"

        self.logger.info("----- streaming request -----")
        stream = self.client.chat.completions.create(
            model=DEEPSEEK_MODEL,
            messages=[
                {"role": "system", "content": role},
                {"role": "user", "content": prompt},
            ],
            stream=True,
        )

        for chunk in stream:
            if not chunk.choices:
                continue
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content

    def generate_llm_report(self, prediction_data):
        """
        调用大模型API生成分析报告
        :param prediction_data: 预测结果数据
        :return: 大模型生成的分析报告
        """
        try:
            # 使用流式调用收集完整报告
            report = ""
            for chunk in self.stream_large_model(prediction_data):
                report += chunk

            return {"status": "success", "report": report}
        except Exception as e:
            self.logger.error(f"调用大模型API失败: {e}")
            return {
                "status": "error",
                "report": f"生成报告失败: {str(e)}",
                "error": str(e),
            }


# 全局AI服务实例
ai_service = AIService()
