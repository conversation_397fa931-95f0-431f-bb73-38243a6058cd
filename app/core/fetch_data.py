import asyncio
import json
from datetime import datetime, timezone
import time
from typing import Any, Dict, List, Optional
import requests
from app.logger_config import get_module_logger
from app.crud import bar_data_service

logger = get_module_logger("fetch_data")


def get_current_timestamp_ms() -> int:
    """获取当前时间的毫秒时间戳"""
    return int(datetime.now(timezone.utc).timestamp() * 1000)


def format_timestamp(timestamp_ms: int) -> str:
    """将毫秒时间戳转换为可读格式"""
    return datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc).strftime(
        "%Y-%m-%d %H:%M:%S"
    )


def process_data_for_db(raw_data: List[Dict], symbol: str) -> List[Dict[str, Any]]:
    """将原始数据转换为数据库格式"""
    processed_data = []
    for item in raw_data:
        processed_item = {
            "time": datetime.fromtimestamp(item["time"] / 1000, tz=timezone.utc),
            "symbol": symbol,
            "open": float(item["open"]),
            "high": float(item["high"]),
            "low": float(item["low"]),
            "close": float(item["close"]),
            "volume": float(item["volume"]),
        }
        processed_data.append(processed_item)
    return processed_data


def make_request(end_time: int, symbol) -> Optional[Dict[Any, Any]]:
    """发起HTTP请求获取数据"""
    base_url = "https://financego.x-funds.com/infos/ebar/getHisBarListWithCount.action"
    params = {
        "source": "MT4",
        "count": 1001,
        "type": 60,
        "endTime": end_time,
        "symbol": symbol,
        "_": get_current_timestamp_ms(),
    }
    try:
        response = requests.get(base_url, params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        get_module_logger("fetch_data").error(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        get_module_logger("fetch_data").error(f"JSON解析失败: {e}")
        return None


async def crawl_all_data(symbol: str, target_timestamp: int) -> bool:
    """主要爬取逻辑"""
    current_end_time = get_current_timestamp_ms()
    batch_count = 0
    total_processed = 0

    logger.info(f"开始爬取{symbol}数据...")
    logger.info(f"目标时间: {format_timestamp(target_timestamp)}")
    logger.info(f"当前时间: {format_timestamp(current_end_time)}")
    logger.info("-" * 50)

    while current_end_time > target_timestamp:
        batch_count += 1
        logger.info(
            f"第 {batch_count} 批次 - endTime: {format_timestamp(current_end_time)}"
        )
        response_data = make_request(current_end_time, symbol)
        if response_data is None:
            logger.warning("请求失败，跳过此批次")
            current_end_time = int(current_end_time * 0.95)
            continue
        if "data" not in response_data:
            logger.error("响应数据格式异常，没有找到 'data' 字段")
            break
        data_list = response_data["data"]
        if not data_list:
            logger.info("没有更多数据，停止爬取")
            break
        processed_data = process_data_for_db(data_list, symbol)
        bar_data_service.save_bar_data(processed_data, symbol)
        total_processed += len(processed_data)
        logger.info(f"本批次处理并保存 {len(processed_data)} 条数据")
        try:
            time_stamps = [record["time"] for record in data_list if "time" in record]
            if not time_stamps:
                logger.error("数据格式异常，无法获取时间字段")
                break
            next_end_time = min(time_stamps)
            logger.info(
                f"本批次时间范围: {format_timestamp(max(time_stamps))} 到 {format_timestamp(next_end_time)}"
            )
            if next_end_time <= target_timestamp:
                logger.info(f"已达到目标时间: {format_timestamp(next_end_time)}")
                break
            current_end_time = next_end_time
            logger.info(f"下一批次endTime: {format_timestamp(current_end_time)}")
        except (KeyError, TypeError) as e:
            logger.error(f"数据格式异常: {e}")
            break
        await asyncio.sleep(1)
        logger.info("-" * 30)
    logger.info(f"\n爬取完成！")
    logger.info(f"总批次数: {batch_count}")
    logger.info(f"总处理数据条数: {total_processed}")
    return total_processed > 0


def make_request_with_rate(symbol: str , target_timestamp: int =-1) -> Optional[Dict[Any, Any]]:
    """请求带rate的K线数据接口"""
    base_url = (
        "https://financego.x-funds.com/infos/ebar/getBarListByPrevTimeWithRate.action"
    )
    params = {
        "source": "MT4",
        "count": 1001,
        "type": 60,
        "symbol": symbol,
        "startTime": target_timestamp,
        "_": get_current_timestamp_ms(),
    }
    try:
        response = requests.get(base_url, params=params, timeout=10)
        logger.info(f"访问的url: {response.url}")
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        get_module_logger("fetch_data").error(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        get_module_logger("fetch_data").error(f"JSON解析失败: {e}")
        return None


def process_bar_data_with_rate(raw_data: Dict, symbol: str) -> Dict[str, Any]:
    """处理带rate的接口返回数据，返回dict包含rate和bars"""
    result = {"rate": None, "bars": []}
    if not raw_data or "data" not in raw_data:
        return result
    data = raw_data["data"]
    result["rate"] = data.get("rate")
    bars = data.get("bars", [])
    for item in bars:
        processed_item = {
            "time": datetime.fromtimestamp(item["time"] / 1000),
            "symbol": symbol,
            "open": float(item["open"]),
            "high": float(item["high"]),
            "low": float(item["low"]),
            "close": float(item["close"]),
            "volume": float(item["volume"]),
        }
        result["bars"].append(processed_item)
    return result


def crawl_recent_bar_data(symbol: str, start_time: int) -> Dict[str, Any]:
    """
    爬取带rate的K线数据，返回dict，包含rate和bars
    入参：symbol（如EURUSD），start_time（毫秒时间戳）
    """
    logger = get_module_logger("fetch_data")
    logger.info(
        f"开始爬取带rate的K线数据: symbol={symbol}, start_time={format_timestamp(start_time)}"
    )

    response_data = make_request_with_rate(symbol, start_time)
    print(response_data)
    if response_data is None:
        logger.error("请求失败，未获取到数据")
        return {"rate": None, "bars": []}
    result = process_bar_data_with_rate(response_data, symbol)
    logger.info(f"K线条数: {len(result['bars'])}")
    return result
