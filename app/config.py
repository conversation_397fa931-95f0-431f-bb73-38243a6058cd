"""
配置文件
"""

import os

# 文件路径配置
HISTORY_FILE = "static/history.json"  # 保留用于向后兼容
PKL_DIR = "data/pkl"
SQL_FILE = "data/sqlite_db"

DB_PATH = "prediction.db"

# AI模型配置
DEEPSEEK_V3_KEY = "***********************************"
DEEPSEEK_URL = "https://api.deepseek.com"
DEEPSEEK_MODEL = "deepseek-chat"

# 定时任务配置
DATA_FETCH_DAYS = 3  # 获取最近几天的数据

# 模型训练配置
SYMBOLS = ["XAUUSD"]  # 支持的交易对列表
TRAINING_START_DATE = "2021-01-01"
TRAINING_START_HOUR = 0
TRAINING_END_HOUR = 7

# Flask配置
FLASK_HOST = "0.0.0.0"
FLASK_PORT = 5006
FLASK_DEBUG = True


# 确保目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [PKL_DIR, SQL_FILE, "static"]
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)


# 初始化时确保目录存在
ensure_directories()
