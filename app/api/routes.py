"""
API路由模块
"""

import json
from datetime import datetime, timedelta

from flask import request, jsonify, Response, stream_with_context, send_from_directory

from app.core import ai_service
from app.crud import prediction_history_service


def register_routes(app):
    """注册所有路由"""

    @app.route("/")
    def index():
        """返回默认的 HTML 页面"""
        return send_from_directory(app.static_folder, "index.html")



    @app.route("/api/history/<symbol>", methods=["GET"])
    def get_history_by_symbol(symbol):
        """根据交易对获取预测历史数据"""
        try:
            period = request.args.get('period', '7')  # 新增统计周期参数
            
            # 计算日期范围
            end_date = datetime.now()
            if period == 'all':
                start_date = None
            else:
                try:
                    days = int(period)
                    start_date = end_date - timedelta(days=days)
                except ValueError:
                    start_date = end_date - timedelta(days=7)  # 默认30天
            
            # 使用prediction_history_service获取预测历史数据
            predictions = prediction_history_service.list_all(symbol)
            
            # 按时间范围过滤数据
            if start_date:
                filtered_predictions = []
                for pred in predictions:
                    try:
                        pred_date = datetime.strptime(pred['Date'], '%Y-%m-%d')
                        if pred_date >= start_date:
                            filtered_predictions.append(pred)
                    except ValueError:
                        # 如果日期格式错误，跳过该记录
                        continue
                predictions = filtered_predictions
            
            # 按日期降序排序
            predictions.sort(key=lambda x: x['Date'], reverse=True)
            
            return jsonify(predictions)

        except Exception as e:
            return jsonify({"error": f"获取{symbol}历史数据失败: {str(e)}"}), 500





    @app.route("/stream_ai_report/<date>")
    def stream_ai_report(date):
        """流式返回指定日期的AI报告"""

        def generate():
            try:
                symbol = request.args.get('symbol', 'EURUSD')
                
                # 使用prediction_history_service获取预测数据
                entry = prediction_history_service.get_record(date, symbol)

                if not entry:
                    yield f"data: {json.dumps({'type': 'error', 'message': '未找到该日期的预测记录'})}\n\n"
                    return

                # 检查是否已有AI报告
                if (
                    "AI_Report" in entry
                    and entry["AI_Report"]
                    and entry["AI_Report"].get("status") == "success"
                ):
                    # 将现有报告分段发送，模拟流式效果
                    report = entry["AI_Report"]["report"]
                    # 直接发送完整报告，让前端处理Markdown格式
                    yield f"data: {json.dumps({'type': 'content', 'content': report})}\n\n"
                    yield f"data: {json.dumps({'type': 'done'})}\n\n"
                    return

                # 如果没有报告，使用stream_large_model实时生成
                full_report = ""

                try:
                    # 使用已定义的流式大模型函数
                    for chunk in ai_service.stream_large_model(entry):
                        if chunk:
                            full_report += chunk
                            yield f"data: {json.dumps({'type': 'content', 'content': chunk})}\n\n"

                    # 保存完整报告到数据库
                    existing_record = prediction_history_service.get_prediction_record(symbol, date)
                    if existing_record:
                        existing_record.ai_report_status = "success"
                        existing_record.ai_report_content = full_report
                        existing_record.ai_report_error = None
                        prediction_history_service.session.commit()

                    yield f"data: {json.dumps({'type': 'done'})}\n\n"

                except Exception as e:
                    from app.logger_config import get_logger
                    logger = get_logger()
                    logger.error(f"调用大模型API失败: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'message': f'生成报告失败: {str(e)}'})}\n\n"

            except Exception as e:
                yield f"data: {json.dumps({'type': 'error', 'message': f'获取AI报告失败: {str(e)}'})}\n\n"

        return Response(
            stream_with_context(generate()), content_type="text/event-stream"
        )


