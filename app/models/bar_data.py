from sqlalchemy import create_engine, Column, String, Float, DateTime, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.config import SQL_FILE, DB_PATH

Base = declarative_base()


class BarData(Base):
    """市场数据模型"""

    __tablename__ = "market_data"

    time = Column(DateTime, primary_key=True)  # 时间作为主键的一部分
    symbol = Column(String(10), primary_key=True, nullable=False)  # 交易对符号作为主键的一部分
    open = Column(Float, nullable=False)  # 开盘价
    high = Column(Float, nullable=False)  # 最高价
    low = Column(Float, nullable=False)  # 最低价
    close = Column(Float, nullable=False)  # 收盘价
    volume = Column(Integer, nullable=True)  # 成交量

    def to_dict(self):
        """转换为字典格式"""
        return {
            "time": self.time,
            "symbol": self.symbol,
            "open": self.open,
            "high": self.high,
            "low": self.low,
            "close": self.close,
            "volume": self.volume,
        }


# 创建数据库引擎和会话
import os

# 确保数据库目录存在
db_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), SQL_FILE)
if not os.path.exists(db_dir):
    os.makedirs(db_dir)

# 使用绝对路径创建数据库
db_path = os.path.join(db_dir, DB_PATH)
engine = create_engine(f"sqlite:///{db_path}")
Session = sessionmaker(bind=engine)


def init_db():
    """初始化数据库"""
    Base.metadata.create_all(engine)
