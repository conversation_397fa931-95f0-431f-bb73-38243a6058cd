from sqlalchemy import Column, String, Float, DateTime, Text
from datetime import datetime

# 使用与BarData相同的Base和引擎
from app.models.bar_data import Base, engine, Session


class PredictionHistory(Base):
    """预测历史数据模型"""

    __tablename__ = "prediction_history"

    date = Column(String(10), primary_key=True)  # 日期 (YYYY-MM-DD)
    symbol = Column(String(10), primary_key=True, nullable=False)  # 交易对符号
    morning_price = Column(Float, nullable=True)  # 09:00价格
    predicted_direction = Column(String(10), nullable=True)  # 预测方向
    predicted_price = Column(Float, nullable=True)  # 预测16:00价格
    predicted_magnitude = Column(Float, nullable=True)  # 预测幅度(%)
    price_difference = Column(Float, nullable=True)  # 价格差异
    actual_price = Column(Float, nullable=True)  # 实际16:00价格
    actual_direction = Column(String(10), nullable=True)  # 实际方向
    price_diff_actual_predicted = Column(Float, nullable=True)  # 实际-预测价格差异
    deviation_percentage = Column(Float, nullable=True)  # 偏离度(%)
    ai_report_status = Column(String(20), nullable=True)  # AI报告状态
    ai_report_content = Column(Text, nullable=True)  # AI报告内容
    ai_report_error = Column(Text, nullable=True)  # AI报告错误信息
    created_at = Column(DateTime, default=datetime.utcnow)  # 创建时间
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)  # 更新时间

    def to_dict(self):
        """转换为字典格式"""
        return {
            "Date": self.date,
            "Symbol": self.symbol,
            "09:00 Price": self.morning_price,
            "Predicted Direction": self.predicted_direction,
            "Predicted 16:00 Price": self.predicted_price,
            "Predicted Magnitude (%)": self.predicted_magnitude,
            "Price Difference": self.price_difference,
            "Actual 16:00 Price": self.actual_price,
            "Actual Direction": self.actual_direction,
            "Price Difference (Actual-Predicted)": self.price_diff_actual_predicted,
            "Deviation (%)": self.deviation_percentage,
            "AI_Report": {
                "status": self.ai_report_status,
                "report": self.ai_report_content,
                "error": self.ai_report_error
            } if self.ai_report_status else None,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: dict):
        """从字典创建实例"""
        # 处理AI报告
        ai_report = data.get("AI_Report", {})
        if isinstance(ai_report, dict):
            ai_report_status = ai_report.get("status")
            ai_report_content = ai_report.get("report")
            ai_report_error = ai_report.get("error")
        else:
            ai_report_status = None
            ai_report_content = None
            ai_report_error = None

        # 处理实际价格
        actual_price = data.get("Actual 16:00 Price")
        if actual_price == "N/A":
            actual_price = None

        # 处理价格差异
        price_diff = data.get("Price Difference (Actual-Predicted)")
        if price_diff == "N/A":
            price_diff = None

        # 处理偏离度
        deviation = data.get("Deviation (%)")
        if deviation == "N/A":
            deviation = None

        return cls(
            date=data.get("Date"),
            symbol=data.get("Symbol", "EURUSD"),
            morning_price=data.get("09:00 Price"),
            predicted_direction=data.get("Predicted Direction"),
            predicted_price=data.get("Predicted 16:00 Price"),
            predicted_magnitude=data.get("Predicted Magnitude (%)"),
            price_difference=data.get("Price Difference"),
            actual_price=actual_price,
            actual_direction=data.get("Actual Direction") if data.get("Actual Direction") != "N/A" else None,
            price_diff_actual_predicted=price_diff,
            deviation_percentage=deviation,
            ai_report_status=ai_report_status,
            ai_report_content=ai_report_content,
            ai_report_error=ai_report_error
        )


def init_prediction_history_db():
    """初始化预测历史数据库表"""
    Base.metadata.create_all(engine)
