#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块
提供统一的日志记录功能，支持不同级别的日志输出
"""

import logging
import os
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler


def setup_logger(name="gold_prediction", log_level=logging.INFO):
    """
    设置日志记录器

    Args:
        name (str): 日志记录器名称
        log_level: 日志级别

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 创建log目录
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs")
    os.makedirs(log_dir, exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(log_level)

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    # 创建格式化器
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器 - 按天轮转，文件名格式为 xxxx-xx-xx-logs.log
    log_filename = os.path.join(log_dir, f"{datetime.now().strftime('%Y-%m-%d')}-logs.log")
    file_handler = TimedRotatingFileHandler(
        log_filename,
        when="midnight",
        interval=1,
        backupCount=30,
        encoding="utf-8"
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    file_handler.suffix = "%Y-%m-%d"
    logger.addHandler(file_handler)

    return logger


def get_logger(name="gold_prediction"):
    """
    获取日志记录器

    Args:
        name (str): 日志记录器名称

    Returns:
        logging.Logger: 日志记录器
    """
    logger = logging.getLogger(name)
    if not logger.handlers:
        setup_logger(name)
    return logger


# 创建默认日志记录器
default_logger = get_logger()


# 为不同模块创建专门的日志记录器
def get_module_logger(module_name):
    """
    获取模块专用的日志记录器

    Args:
        module_name (str): 模块名称

    Returns:
        logging.Logger: 模块日志记录器
    """
    return get_logger(f"gold_prediction.{module_name}")


# 便捷的日志记录函数
def log_info(message, logger=None):
    """记录信息日志"""
    if logger is None:
        logger = default_logger
    logger.info(message)


def log_warning(message, logger=None):
    """记录警告日志"""
    if logger is None:
        logger = default_logger
    logger.warning(message)


def log_error(message, logger=None):
    """记录错误日志"""
    if logger is None:
        logger = default_logger
    logger.error(message)


def log_debug(message, logger=None):
    """记录调试日志"""
    if logger is None:
        logger = default_logger
    logger.debug(message)


def log_critical(message, logger=None):
    """记录严重错误日志"""
    if logger is None:
        logger = default_logger
    logger.critical(message)
