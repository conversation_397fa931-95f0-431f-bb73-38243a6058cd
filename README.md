# 黄金预测模型 Service

这是一个基于SVR（支持向量回归）的交易预测模型服务，提供完整的模型创建和训练功能。

## 功能特性

- **技术指标计算**: 自动计算MA、RSI、MACD、ATR、ADX等技术指标
- **特征工程**: 自动生成滞后特征，捕捉时间序列模式
- **数据预处理**: 支持时间过滤、数据聚合等预处理操作
- **模型训练**: 使用SVR模型进行回归预测
- **模型评估**: 提供MSE、MAE、胜率等评估指标
- **模型保存**: 自动保存训练好的模型

## 文件结构

```
├── creatMod.py                    # 主要服务类文件
├── service_usage_example.py       # 使用示例
└── README.md                     # 说明文档
```

## 核心类：ModelService

### 初始化

```python
from creatMod import ModelService

# 创建服务实例
model_service = ModelService()
```

### 主要方法

#### 1. `create_model(symbol, data, model_save_dir)`

创建并训练预测模型的主要方法。

**参数:**
- `symbol` (str): 交易品种符号，如 'XAUUSD', 'EURUSD' 等
- `data` (DataFrame): 输入的OHLCV数据，必须包含 Date, Time, Open, High, Low, Close, Volume 列
- `model_save_dir` (str): 模型保存目录

**使用示例:**
```python
# 加载数据
data = model_service.load_data("XAUUSD60.csv")

# 创建模型
model_service.create_model(
    symbol="XAUUSD",
    data=data,
    model_save_dir="./models"
)
```

#### 2. `load_data(file_path)`

从CSV文件加载数据并进行基本预处理。

**参数:**
- `file_path` (str): CSV文件路径

**返回:**
- DataFrame: 处理后的数据

#### 3. `add_indicators(data)`

为数据添加技术指标。

**参数:**
- `data` (DataFrame): 原始数据

**返回:**
- DataFrame: 包含技术指标的数据

#### 4. `add_shifted_columns(data, columns, lags)`

为指定列添加滞后特征。

**参数:**
- `data` (DataFrame): 原始数据
- `columns` (list): 需要添加滞后的列名列表
- `lags` (range): 滞后期数范围

**返回:**
- DataFrame: 包含滞后特征的数据

#### 5. `filter_and_group_data(data, start_date, start_hour, end_hour)`

过滤和分组数据。

**参数:**
- `data` (DataFrame): 原始数据
- `start_date` (str): 开始日期，默认 '2021-01-01'
- `start_hour` (int): 开始小时，默认 0
- `end_hour` (int): 结束小时，默认 7

**返回:**
- DataFrame: 处理后的数据

#### 6. `train_models(df_grouped, svm_model_path)`

训练SVR模型。

**参数:**
- `df_grouped` (DataFrame): 包含特征和目标变量的数据
- `svm_model_path` (str): 模型保存路径

**返回:**
- tuple: (模型, 评估指标字典)

## 使用方法

### 基本用法

```python
from creatMod import ModelService

# 创建服务实例
model_service = ModelService()

# 加载数据
data = model_service.load_data("XAUUSD60.csv")

# 创建模型
model_service.create_model(
    symbol="XAUUSD",
    data=data,
    model_save_dir="./models"
)
```

### 为多个品种创建模型

```python
model_service = ModelService()
symbols = ['XAUUSD', 'EURUSD', 'GBPUSD']

for symbol in symbols:
    try:
        # 加载数据
        data = model_service.load_data(f"{symbol}60.csv")
        
        # 创建模型
        model_service.create_model(
            symbol=symbol,
            data=data,
            model_save_dir=f"./models/{symbol}"
        )
    except Exception as e:
        print(f"处理 {symbol} 时出错: {e}")
```

### 分步骤处理数据

```python
model_service = ModelService()

# 1. 加载数据
data = model_service.load_data("XAUUSD60.csv")

# 2. 添加技术指标
data_with_indicators = model_service.add_indicators(data)

# 3. 添加滞后特征
columns_to_shift = ['pct_chg', 'MA20', 'RSI', 'MACD', 'Signal', 'ATR', '7H_Mean_Change', '7H_Volatility', 'MA5-MA20', 'ADX']
lags = range(0, 11)
data_with_lags = model_service.add_shifted_columns(data_with_indicators, columns_to_shift, lags)

# 4. 过滤和分组
grouped_data = model_service.filter_and_group_data(data_with_lags)

# 5. 训练模型
model, metrics = model_service.train_models(grouped_data, "./models/XAUUSD_model.pkl")
```

## 数据格式要求

输入数据必须包含以下列：
- `Date`: 日期 (格式: YYYY-MM-DD)
- `Time`: 时间 (格式: HH:MM)
- `Open`: 开盘价
- `High`: 最高价
- `Low`: 最低价
- `Close`: 收盘价
- `Volume`: 成交量

## 技术指标说明

模型使用以下技术指标作为特征：
- **MA20**: 20期移动平均线
- **RSI**: 相对强弱指数 (14期)
- **MACD**: 移动平均收敛发散指标
- **ATR**: 平均真实波幅 (14期)
- **ADX**: 平均方向指数 (14期)
- **7H_Mean_Change**: 7期价格变化均值
- **7H_Volatility**: 7期价格波动率
- **MA5-MA20**: 5期与20期移动平均线差值

## 模型特点

- **特征维度**: 110个特征 (10个指标 × 11个滞后周期)
- **算法**: SVR (支持向量回归) 使用RBF核函数
- **评估指标**: MSE、MAE、胜率
- **数据要求**: 建议至少100条记录用于训练

## 依赖库

```bash
pip install pandas numpy scikit-learn joblib
```

## 运行示例

```bash
# 运行主程序
python creatMod.py

# 运行使用示例
python service_usage_example.py
```

## 注意事项

1. 确保输入数据质量良好，包含足够的历史数据
2. 模型训练需要一定时间，请耐心等待
3. 建议定期重新训练模型以适应市场变化
4. 模型预测结果仅供参考，不构成投资建议
5. 使用service时，所有方法调用都需要通过实例进行

## 错误处理

服务包含完善的错误处理机制：
- 数据量不足检查
- 文件不存在处理
- 数据格式验证
- 模型训练异常处理

## 扩展性

ModelService类设计为可扩展的，您可以：
- 继承ModelService类添加新的技术指标
- 重写train_models方法使用不同的机器学习算法
- 添加新的数据预处理方法
- 扩展模型评估指标 