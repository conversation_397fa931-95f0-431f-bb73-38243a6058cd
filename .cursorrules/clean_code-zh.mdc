---
alwaysApply: true
---
# 代码整洁指南

## 常量优于魔法数字
- 用命名常量替换硬编码值
- 使用描述性常量名称来解释值的作用
- 将常量放在文件顶部或专用常量文件中

## 有意义的名称
- 变量、函数和类应该揭示其作用
- 名称应该解释某个函数存在的原因及其用法
- 避免使用缩写，除非它们被普遍理解

## 智能注释
- 不要注释代码的功能 - 使代码具有自文档性
- 使用注释来解释为什么以某种方式执行某些操作
- 记录 API、复杂算法和不明显的副作用

## 单一职责
- 每个函数应该只做一件事
- 函数应该简短且专注
- 如果一个函数需要注释来解释其功能，则应该将其拆分

## DRY（不要重复自己）
- 将重复的代码提取到可复用的函数中
-通过适当的抽象共享通用逻辑
- 维护单一事实来源

## 清晰的结构
- 将相关代码放在一起
- 按逻辑层次结构组织代码
- 使用一致的文件和文件夹命名约定

## 封装
- 隐藏实现细节
- 公开清晰的接口
- 将嵌套条件语句移至命名良好的函数中

## 代码质量维护
- 持续重构
- 尽早修复技术债务
- 使代码比最初更整洁

## 测试
- 在修复错误之前编写测试
- 保持测试的可读性和可维护性
- 测试边缘情况和错误条件

## 版本控制
- 编写清晰的提交信息
- 进行简短、有针对性的提交
- 使用有意义的分支名称