#!/usr/bin/env python3
"""
每日模型训练脚本
从2024年12月30日到2025年3月28日，每日调用make_model函数进行模型训练
"""

import sys
import os
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config import SYMBOLS
from app.core import scheduler_service
from app.utils.logger import get_module_logger


class DailyModelTrainer:
    """每日模型训练器"""
    
    def __init__(self):
        self.logger = get_module_logger("daily_model_trainer")
        self.start_date = datetime(2024, 12, 30)
        self.end_date = datetime(2025, 3, 28)
        
    def generate_date_range(self) -> List[datetime]:
        """生成日期范围列表"""
        dates = []
        current_date = self.start_date
        
        while current_date <= self.end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
            
        return dates
    
    def make_model(self, symbol: str, date: datetime) -> bool:
        """
        为指定交易对和日期训练模型
        
        Args:
            symbol: 交易对符号
            date: 训练日期
            
        Returns:
            bool: 训练是否成功
        """
        try:
            self.logger.info(f"开始为 {symbol} 训练 {date.strftime('%Y-%m-%d')} 的模型")
            result = scheduler_service.train_and_save_model(symbol, date)
            
            if result:
                self.logger.info(f"✅ {symbol} {date.strftime('%Y-%m-%d')} 模型训练成功")
            else:
                self.logger.error(f"❌ {symbol} {date.strftime('%Y-%m-%d')} 模型训练失败")
                
            return result
            
        except Exception as e:
            self.logger.error(f"❌ {symbol} {date.strftime('%Y-%m-%d')} 模型训练异常: {e}")
            return False
    
    def train_all_models(self):
        """训练所有日期和交易对的模型"""
        dates = self.generate_date_range()
        total_dates = len(dates)
        total_symbols = len(SYMBOLS)
        total_tasks = total_dates * total_symbols
        
        self.logger.info(f"开始批量模型训练任务")
        self.logger.info(f"日期范围: {self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        self.logger.info(f"交易对: {SYMBOLS}")
        self.logger.info(f"总计: {total_dates} 天 × {total_symbols} 个交易对 = {total_tasks} 个训练任务")
        self.logger.info("=" * 80)
        
        success_count = 0
        failed_count = 0
        
        for date_index, date in enumerate(dates, 1):
            self.logger.info(f"\n📅 处理日期: {date.strftime('%Y-%m-%d')} ({date_index}/{total_dates})")
            self.logger.info("-" * 60)
            
            for symbol_index, symbol in enumerate(SYMBOLS, 1):
                self.logger.info(f"🔄 处理交易对: {symbol} ({symbol_index}/{total_symbols})")
                
                if self.make_model(symbol, date):
                    success_count += 1
                else:
                    failed_count += 1
                    
                # 显示进度
                completed_tasks = (date_index - 1) * total_symbols + symbol_index
                progress = (completed_tasks / total_tasks) * 100
                self.logger.info(f"📊 总进度: {completed_tasks}/{total_tasks} ({progress:.1f}%)")
        
        # 输出最终统计
        self.logger.info("\n" + "=" * 80)
        self.logger.info("🎉 批量模型训练任务完成!")
        self.logger.info(f"✅ 成功: {success_count} 个任务")
        self.logger.info(f"❌ 失败: {failed_count} 个任务")
        self.logger.info(f"📊 成功率: {(success_count / total_tasks * 100):.1f}%")
        self.logger.info("=" * 80)
    
    def train_single_date(self, target_date: str):
        """训练指定日期的所有交易对模型"""
        try:
            date = datetime.strptime(target_date, '%Y-%m-%d')
            
            if date < self.start_date or date > self.end_date:
                self.logger.warning(f"日期 {target_date} 不在指定范围内 ({self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')})")
                return
                
            self.logger.info(f"开始训练 {target_date} 的所有交易对模型")
            
            success_count = 0
            for symbol in SYMBOLS:
                if self.make_model(symbol, date):
                    success_count += 1
                    
            self.logger.info(f"日期 {target_date} 训练完成: {success_count}/{len(SYMBOLS)} 个交易对成功")
            
        except ValueError:
            self.logger.error(f"日期格式错误: {target_date}，请使用 YYYY-MM-DD 格式")
    
    def train_single_symbol_all_dates(self, symbol: str):
        """训练指定交易对的所有日期模型"""
        if symbol not in SYMBOLS:
            self.logger.error(f"交易对 {symbol} 不在支持列表中: {SYMBOLS}")
            return
            
        dates = self.generate_date_range()
        self.logger.info(f"开始训练交易对 {symbol} 的所有日期模型 ({len(dates)} 天)")
        
        success_count = 0
        for date in dates:
            if self.make_model(symbol, date):
                success_count += 1
                
        self.logger.info(f"交易对 {symbol} 训练完成: {success_count}/{len(dates)} 天成功")


def main():
    """主函数"""
    trainer = DailyModelTrainer()
    
    if len(sys.argv) == 1:
        # 无参数：训练所有日期和交易对
        trainer.train_all_models()
    elif len(sys.argv) == 2:
        arg = sys.argv[1]
        if arg in SYMBOLS:
            # 参数是交易对：训练该交易对的所有日期
            trainer.train_single_symbol_all_dates(arg)
        else:
            # 参数是日期：训练该日期的所有交易对
            trainer.train_single_date(arg)
    else:
        print("用法:")
        print("  python daily_model_training.py                    # 训练所有日期和交易对")
        print("  python daily_model_training.py 2024-12-30         # 训练指定日期的所有交易对")
        print("  python daily_model_training.py XAUUSD             # 训练指定交易对的所有日期")
        print(f"  支持的交易对: {SYMBOLS}")


if __name__ == "__main__":
    main()
