[project]
name = "gold-prediction"
version = "3.0.0"
description = "Gold price prediction system"
readme = "README.md"
requires-python = ">=3.12"

dependencies = [
    "pandas>=1.5.0",
    "numpy>=1.21.0",
    "scikit-learn>=1.1.0",
    "joblib>=1.2.0",
    "flask>=2.3.0",
    "flask-cors>=4.0.0",
    "requests>=2.28.0",
#    "volcengine-python-sdk[ark]",
    "openai>=1.0.0",
    "sqlalchemy>=2.0.0",
    "schedule>=1.2.0",
    "pytz>=2023.3",
]

[project.optional-dependencies]
dev = [
  "black",
  "ruff",
]


[tool.black]
line-length = 88
target-version = ["py38"]
include = '\.pyi?$'
exclude = '''
/(
    \.venv
  | build
  | dist
  | \.git
  | \.mypy_cache
  | \.ruff_cache
)/
'''

[tool.ruff]
line-length = 88
target-version = "py38"

[tool.ruff.lint]
select = ["E", "W", "F", "I", "B", "C4", "UP"]
ignore = ["E501", "B008", "C901", "W191"]
fixable = ["ALL"]
unfixable = []

[tool.ruff.lint.isort]
known-first-party = ["my_python_project"]
force-single-line = false
combine-as-imports = true
